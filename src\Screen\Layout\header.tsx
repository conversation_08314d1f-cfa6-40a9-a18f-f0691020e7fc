import {
  GestureResponderEvent,
  StyleSheet,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {Text} from 'react-native-paper';
import {TypoSkin} from '../../assets/skin/typography';
import {ColorThemes} from '../../assets/skin/colors';
import {Winicon} from 'wini-mobile-components';
import {useTranslation} from 'react-i18next';

export default function ScreenHeader({
  onBack,
  title,
  action,
  height,
  bottom,
  children,
  backIcon,
  style = {},
  titleStyle = {},
  prefix,
}: {
  onBack?: ((event: GestureResponderEvent) => void) & (() => void);
  title?: React.ReactNode | string;
  action?: React.ReactNode;
  bottom?: React.ReactNode;
  height?: number;
  children?: React.ReactNode;
  backIcon?: React.ReactNode;
  prefix?: React.ReactNode;
  style?: ViewStyle;
  titleStyle?: TextStyle;
}) {
  const {t} = useTranslation();
  return (
    <View style={[styles.container, style]}>
      {children ?? (
        <View
          style={{
            ...styles.header,
            height: height ?? style.height ?? 56,
          }}>
          {prefix}
          {onBack ? (
            <TouchableOpacity
              style={{
                paddingLeft: 16,
                paddingRight: 16,
                paddingVertical: 8,
                alignItems: 'center',
                zIndex: 10,
              }}
              activeOpacity={0.7}
              hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
              onPress={event => {
                try {
                  console.log('ScreenHeader: back button pressed');
                  if (onBack) {
                    onBack(event);
                  }
                } catch (error) {
                  console.error('Error in ScreenHeader onPress:', error);
                }
              }}>
              {backIcon ?? (
                <View
                  style={{
                    gap: 4,
                    flexDirection: 'row',
                    width: '100%',
                    alignItems: 'center',
                    borderRadius: 20,
                    padding: 6,
                  }}>
                  <Winicon
                    src="outline/arrows/left-arrow"
                    size={20}
                    color={ColorThemes.light.neutral_text_title_color}
                  />
                </View>
              )}
            </TouchableOpacity>
          ) : undefined}
          <View style={styles.title}>
            {typeof title === 'string' ? (
              <Text
                numberOfLines={2}
                style={[
                  TypoSkin.title3,
                  {
                    textAlign: 'center',
                    color: ColorThemes.light.neutral_text_title_color,
                    ...titleStyle,
                  },
                ]}>
                {title ?? '-'}
              </Text>
            ) : (
              title ?? <View />
            )}
          </View>
          {action}
        </View>
      )}
      {bottom}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: ColorThemes.light.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    alignSelf: 'center',
  },
  title: {
    position: 'absolute',
    left: '12%',
    right: '12%',
  },
});
