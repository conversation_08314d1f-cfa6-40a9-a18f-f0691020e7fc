import React, { use, useEffect, useState } from 'react';
import { View, Image, Text, TouchableOpacity, StyleSheet, Pressable } from 'react-native';
import { FlatList, ScrollView } from 'react-native-gesture-handler';
import { Winicon } from 'wini-mobile-components';
import { TypeMenuReview } from '../../../Config/Contanst';
import { ReviewData, ReviewOrderData } from '../../../mock/shopData';
import { ReviewDataDto, ReviewProductProps } from '../../dto/dto';
import ReviewProductIteCard from '../card/CardReviewItem';
import { DataController } from '../../../base/baseController';
import { useSelectorShopState } from '../../../redux/hook/shopHook ';


const ReviewProduct = (props: ReviewProductProps) => {
    const { type } = props
    const [data, setData] = useState<ReviewDataDto[] | any[]>([])
    const ProductController = new DataController('Product');
    const RatingController = new DataController('Rating');
    const shopInfo = useSelectorShopState().data;
    useEffect(() => {
        if (type == TypeMenuReview.Product) {
            setData(ReviewData)
        } else {
            setData(ReviewOrderData)
        }
    }, [type])
    let callApi = async () => {
        let res = await ProductController.aggregateList({
            searchRaw: `@ShopId:{${shopInfo[0]?.Id}}`,
        })
        if (res?.code === 200) {
            let arrayProduct: string[] = [];
            res.data.forEach((item: any) => {
                arrayProduct.push(item.Id)
            })
            console.log("check-arrayProduct", arrayProduct)
            let resRating = await RatingController.aggregateList({
                searchRaw: `@ProductId: ${arrayProduct}`,
            })
            console.log("check-resRating", resRating)
        }
    }
    useEffect(() => {
        callApi()
    }, [])

    return (
        <FlatList
            data={data}
            style={{ flex: 1 }}
            // contentContainerStyle={{ paddingBottom: 100 }}
            keyExtractor={(item, i) => `${i} ${item.id}`}
            renderItem={({ item, index }) => ReviewProductIteCard({ item, index }, type as string)}
        />
    );
};

const styles = StyleSheet.create({
    wrapper: {
        padding: 10,
    },
    review: {
        flexDirection: 'row',
        padding: 10,
        borderBottomWidth: 0.2,
        borderBottomColor: '#00FFFF',
    },
    avatar: {
        width: 40,
        height: 40,
        borderRadius: 20,
        marginRight: 10,
    },
    reviewContent: {
        flex: 1,
    },
    name: {
        fontWeight: 'bold',
    },
    rating: {
        color: '#FFD700', // Màu vàng cho sao
    },
    description: {
        fontSize: 14,
        marginVertical: 5,
    },
    imagesProduct: {
        flexDirection: 'row',
        marginTop: 10,
        width: "100%",
        height: 100,
    },
    images: {
        flexDirection: 'row',
        marginTop: 10,

    },
    productImage: {
        width: 100,
        height: 100,
        marginRight: 10,
        borderRadius: 5,
    },
    reviewDetail: {
        flexDirection: "row",
        alignContent: "center",
    },
    avatarProduct: {
        width: 80,
        height: 80,
        borderRadius: 20,
        marginRight: 10,
    },
    imageDetail: {
        borderWidth: 5,
        borderRadius: 50,
        width: 50,
        height: 50,
        marginRight: 10,
        borderColor: "#F8F8FF",
        shadowColor: '#000000',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.15,
        shadowRadius: 8,
        elevation: 8,

    },
    avatarDetail: {
        width: 40,
        height: 40,
        padding: 20,
        borderRadius: 50,
    },
    tag: {
        fontSize: 15,
        color: '#555',
    },
    size: {
        fontSize: 12,
        color: '#888',
    },
    orderDetail: {
        flexDirection: "row",
        margin: 10,
    },
    orderName: {
        fontSize: 20,
        color: "blue",
        marginLeft: 5
    }

});

export default ReviewProduct;