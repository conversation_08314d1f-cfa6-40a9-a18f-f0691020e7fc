import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  StatusBar,
  Platform,
  Image,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {
  Winicon,
  ComponentStatus,
  showSnackbar,
  AppSvg,
} from 'wini-mobile-components';
import {TypoSkin} from '../../assets/skin/typography';
import Svg, {Path} from 'react-native-svg';
import FastImage from 'react-native-fast-image';
import ConfigAPI from '../../Config/ConfigAPI';
import {Ultis} from '../../utils/Utils';
import iconSvg from '../../svg/icon';
import {OrderDA} from '../../modules/order/orderDA';
import {StatusOrder} from '../../Config/Contanst';
import {RenderHeaderCart} from './CartPage';
import {SafeAreaView} from 'react-native-safe-area-context';
import ScreenHeader from '../Layout/header';
import {ColorThemes} from '../../assets/skin/colors';

const {width} = Dimensions.get('window');
const STATUSBAR_HEIGHT = StatusBar.currentHeight || 0;

interface OrderDetailRouteParams {
  orderId: string;
}

interface OrderDetail {
  Id: string;
  OrderId: string;
  ProductId: string;
  Quantity: number;
  Price: number;
  Discount: number;
  Status: number;
  Product?: {
    Id: string;
    Name: string;
    Img: string;
    Description?: string;
  };
}

interface Order {
  Id: string;
  CustomerId: string;
  Name: string;
  ShopId: string;
  Code: string;
  DateCreated: number;
  Status: number;
  Total: number;
  Note: string;
  details?: OrderDetail[];
}

const OrderDetailPage: React.FC = () => {
  const navigation = useNavigation<any>();
  const route = useRoute();
  const [loading, setLoading] = useState(true);
  const [order, setOrder] = useState<Order | null>(null);
  const [orderDetails, setOrderDetails] = useState<OrderDetail[]>([]);
  const orderDA = new OrderDA();

  // Lấy orderId từ route params
  const {orderId} = (route.params as OrderDetailRouteParams) || {};

  // Lấy thông tin đơn hàng và chi tiết đơn hàng
  useEffect(() => {
    const fetchOrderData = async () => {
      if (!orderId) {
        setLoading(false);
        return;
      }

      try {
        // Lấy thông tin đơn hàng
        const orderResponse = await orderDA.getOrderByOrderId(orderId);
        if (orderResponse?.code === 200 && orderResponse.data?.length > 0) {
          setOrder(orderResponse.data[0]);

          // Lấy chi tiết đơn hàng
          // Giả định có phương thức getOrderDetailsByOrderId trong OrderDA
          // Trong thực tế, bạn cần thêm phương thức này vào OrderDA
          // const detailsResponse = await orderDA.getOrderDetailsByOrderId(orderId);
          // if (detailsResponse?.code === 200) {
          //   setOrderDetails(detailsResponse.data);
          // }

          // Tạm thời sử dụng dữ liệu mẫu
          setOrderDetails([
            {
              Id: '1',
              OrderId: orderId,
              ProductId: '1',
              Quantity: 1,
              Price: 17.0,
              Discount: 0,
              Status: StatusOrder.new,
              Product: {
                Id: '1',
                Name: 'Áo len cổ tím',
                Img: 'https://placehold.co/100',
                Description: 'Màu tím, size S (x1)',
              },
            },
            {
              Id: '2',
              OrderId: orderId,
              ProductId: '2',
              Quantity: 1,
              Price: 17.0,
              Discount: 0,
              Status: StatusOrder.new,
              Product: {
                Id: '2',
                Name: 'Áo len cổ tím',
                Img: 'https://placehold.co/100',
                Description: 'Màu tím, size S (x1)',
              },
            },
          ]);
        }
      } catch (error) {
        console.error('Error fetching order data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchOrderData();
  }, [orderId]);

  // Render timeline trạng thái đơn hàng
  const renderOrderStatusTimeline = () => {
    const currentStatus = order?.Status || StatusOrder.new;

    return (
      <View style={styles.timelineContainer}>
        {/* Các điểm trạng thái */}
        <View style={styles.timelinePoints}>
          {/* Đặt hàng */}
          <View style={styles.timelinePointWrapper}>
            <View
              style={[
                styles.timelinePoint,
                currentStatus >= StatusOrder.new
                  ? styles.timelinePointActive
                  : {},
              ]}>
              {currentStatus >= StatusOrder.new && (
                <Winicon
                  src="fill/user interface/check"
                  size={12}
                  color="#FFFFFF"
                />
              )}
            </View>
            <Text style={styles.timelineTime}>08:42</Text>
            <Text style={styles.timelineLabel}>Đặt hàng</Text>
          </View>

          {/* Đường nối */}
          <View
            style={[
              styles.timelineConnector,
              currentStatus >= StatusOrder.pending
                ? styles.timelineConnectorActive
                : {},
            ]}
          />

          {/* Chờ lấy hàng */}
          <View style={styles.timelinePointWrapper}>
            <View
              style={[
                styles.timelinePoint,
                currentStatus >= StatusOrder.pending
                  ? styles.timelinePointActive
                  : {},
              ]}>
              {currentStatus >= StatusOrder.pending && (
                <Winicon
                  src="fill/user interface/check"
                  size={12}
                  color="#FFFFFF"
                />
              )}
            </View>
            <Text style={styles.timelineTime}>08:42</Text>
            <Text style={styles.timelineLabel}>Chờ lấy hàng</Text>
          </View>

          {/* Đường nối */}
          <View
            style={[
              styles.timelineConnector,
              currentStatus >= StatusOrder.proccess
                ? styles.timelineConnectorActive
                : {},
            ]}
          />

          {/* Đang vận chuyển */}
          <View style={styles.timelinePointWrapper}>
            <View
              style={[
                styles.timelinePoint,
                currentStatus >= StatusOrder.proccess
                  ? styles.timelinePointActive
                  : {},
              ]}>
              {currentStatus >= StatusOrder.proccess && (
                <Winicon
                  src="fill/user interface/check"
                  size={12}
                  color="#FFFFFF"
                />
              )}
            </View>
            <Text style={styles.timelineTime}>08:42</Text>
            <Text style={styles.timelineLabel}>Đang vận chuyển</Text>
          </View>

          {/* Đường nối */}
          <View
            style={[
              styles.timelineConnector,
              currentStatus >= StatusOrder.success
                ? styles.timelineConnectorActive
                : {},
            ]}
          />

          {/* Hoàn thành */}
          <View style={styles.timelinePointWrapper}>
            <View
              style={[
                styles.timelinePoint,
                currentStatus >= StatusOrder.success
                  ? styles.timelinePointActive
                  : {},
              ]}>
              {currentStatus >= StatusOrder.success && (
                <Winicon
                  src="fill/user interface/check"
                  size={12}
                  color="#FFFFFF"
                />
              )}
            </View>
            <Text style={styles.timelineTime}>08:42</Text>
            <Text style={styles.timelineLabel}>Hoàn thành</Text>
          </View>
        </View>
      </View>
    );
  };

  // Render thông tin đơn hàng
  const renderOrderInfo = () => {
    return (
      <View style={styles.orderInfoContainer}>
        {/* Mã đơn hàng */}
        <View style={styles.orderInfoRow}>
          <View style={styles.orderInfoIcon}>
            <Winicon src="fill/shopping/box" size={16} color="#000000" />
          </View>
          <View style={styles.orderInfoContent}>
            <Text style={styles.orderInfoLabel}>
              Mã đơn hàng: #{order?.Code || '01293213ACB'}
            </Text>
          </View>
          <TouchableOpacity style={styles.copyButton}>
            <Winicon src="fill/files/document-copy" size={16} color="#2962FF" />
          </TouchableOpacity>
        </View>

        {/* Thời gian */}
        <View style={styles.orderInfoRow}>
          <View style={styles.orderInfoIcon}>
            <Winicon
              src="fill/user interface/clock"
              size={16}
              color="#000000"
            />
          </View>
          <View style={styles.orderInfoContent}>
            <Text style={styles.orderInfoLabel}>
              Thời gian:{' '}
              {order?.DateCreated
                ? Ultis.formatDateTime(order.DateCreated)
                : '09:00, ngày 20/04/2025'}
            </Text>
          </View>
        </View>

        {/* Phương thức thanh toán */}
        <View style={styles.orderInfoRow}>
          <View style={styles.orderInfoIcon}>
            <Winicon src="fill/business/wallet-90" size={16} color="#000000" />
          </View>
          <View style={styles.orderInfoContent}>
            <Text style={styles.orderInfoLabel}>PTT: Chuyển khoản</Text>
          </View>
        </View>

        {/* Thông tin người nhận */}
        <View style={styles.orderInfoRow}>
          <View style={styles.orderInfoIcon}>
            <Winicon
              src="fill/shopping/shop-location"
              size={20}
              color="#000000"
            />
          </View>
          <View style={styles.orderInfoContent}>
            <Text style={styles.orderInfoLabel}>
              Nguyễn Thanh Tùng - 0968381635
            </Text>
            <Text style={styles.orderInfoValue}><EMAIL></Text>
            <Text style={styles.orderInfoValue}>
              Villa 05, đường Foresa 4, Xuân Phương, Nam Từ Liêm, Hà Nội
            </Text>
          </View>
        </View>
      </View>
    );
  };

  // Render sản phẩm trong đơn hàng
  const renderOrderItems = () => {
    return (
      <View style={styles.orderItemsContainer}>
        {orderDetails.map((item, index) => (
          <View key={item.Id} style={styles.orderItemRow}>
            <FastImage
              style={styles.productImage}
              source={{
                uri: item.Product?.Img || 'https://placehold.co/100',
                priority: FastImage.priority.normal,
              }}
              resizeMode={FastImage.resizeMode.cover}
            />
            <View style={styles.productDetails}>
              <Text style={styles.productName}>
                {item.Product?.Name || 'Sản phẩm'}
              </Text>
              <Text style={styles.productVariant}>
                {item.Product?.Description || 'Mô tả sản phẩm'}
              </Text>
              <View style={styles.productPriceRow}>
                <Text style={styles.productPrice}>${item.Price}</Text>
                <Text style={styles.originalPrice}>${item.Price + 100}</Text>
              </View>
            </View>
          </View>
        ))}

        {/* Tổng tiền */}
        <View style={styles.totalContainer}>
          <Text style={styles.totalLabel}>
            Tổng tiền ({orderDetails.length} sản phẩm):
          </Text>
          <Text style={styles.totalPrice}>${Ultis.money(34.0)}</Text>
        </View>
      </View>
    );
  };

  // Render các nút hành động
  const renderActionButtons = () => {
    return (
      <View style={styles.actionButtonsContainer}>
        <TouchableOpacity style={styles.cancelButton}>
          <Text style={styles.cancelButtonText}>Hủy đơn</Text>
        </TouchableOpacity>

        <View style={styles.actionButtonsGroup}>
          <TouchableOpacity style={styles.chatButton}>
            <Winicon
              src="outline/user interface/chat"
              size={20}
              color="#000000"
            />
            <Text style={styles.chatButtonText}>Chat với cửa hàng</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.callButton}>
            <Winicon
              src="outline/user interface/phone"
              size={20}
              color="#000000"
            />
            <Text style={styles.callButtonText}>Liên hệ cửa hàng</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView edges={['bottom']} style={styles.container}>
      <RenderHeaderOrder title="Xem chi tiết đơn hàng" />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}>
        {renderOrderStatusTimeline()}
        {renderOrderInfo()}
        {renderOrderItems()}
        <View style={styles.bottomSpacer} />
      </ScrollView>

      {renderActionButtons()}
    </SafeAreaView>
  );
};

export const RenderHeaderOrder = ({title = ''}: {title?: string}) => {
  const navigation = useNavigation<any>();
  return (
    <View style={styles.header}>
      {/* Background màu xanh */}
      <View style={styles.headerBackground}>
        {/* Đường cong ở dưới */}
        <View>
          <Svg width={440} height={100} viewBox="0 0 440 100">
            {/* Sóng hình chữ S thoải và cân đối – cao 100px */}
            <Path
              d="
            M0,50
            C110,0 220,100 330,50
            C385,25 412,50 440,50
            L440,100
            L0,100 Z"
              fill="#84DEEA"
            />
            {/* Nền xanh dương phía trên */}
            <Path
              d="
            M0,0 H440 V50
            C412,50 385,25 330,50
            C220,100 110,0 0,50 Z"
              fill="#1A30FF"
            />
          </Svg>
        </View>
      </View>

      {/* Nội dung header */}
      <SafeAreaView style={styles.headerContent}>
        <ScreenHeader onBack={() => navigation.goBack()} title={title} />
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    width: '100%',
    height: 140,
    position: 'relative',
  },
  headerBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  waveContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 2,
    overflow: 'hidden',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: Platform.OS === 'ios' ? 20 : STATUSBAR_HEIGHT + 10,
    position: 'relative',
    zIndex: 3,
    left: 0,
    right: 0,
  },

  headerActionButton: {
    padding: 8,
    marginLeft: 8,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingBottom: 20,
  },
  timelineContainer: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 20,
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  timelinePoints: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  timelinePointWrapper: {
    alignItems: 'center',
    width: 70,
  },
  timelinePoint: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#E0E0E0',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
  },
  timelinePointActive: {
    backgroundColor: '#4CAF50',
  },
  timelineConnector: {
    marginTop: 12,
    height: 1.5,
    backgroundColor: '#E0E0E0',
    flex: 1,
  },
  timelineConnectorActive: {
    backgroundColor: '#4CAF50',
  },
  timelineTime: {
    ...TypoSkin.body3,
    color: '#757575',
    marginBottom: 2,
  },
  timelineLabel: {
    ...TypoSkin.body3,
    color: '#212121',
    textAlign: 'center',
  },
  orderInfoContainer: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    marginBottom: 8,
  },
  orderInfoRow: {
    flexDirection: 'row',
    padding: 16,
    borderBottomColor: '#EEEEEE',
    borderBottomWidth: 1,
    alignItems: 'center',
  },
  orderInfoIcon: {
    width: 24,
    marginRight: 12,
  },
  orderInfoContent: {
    flex: 1,
  },
  orderInfoLabel: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_title_color,
  },
  orderInfoValue: {
    ...TypoSkin.body3,
    color: '#757575',
  },
  copyButton: {
    padding: 4,
  },
  orderItemsContainer: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    marginBottom: 8,
  },
  orderItemRow: {
    flexDirection: 'row',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  productDetails: {
    flex: 1,
  },
  productName: {
    ...TypoSkin.body2,
    color: '#212121',
    marginBottom: 4,
  },
  productVariant: {
    ...TypoSkin.body3,
    color: '#757575',
    marginBottom: 4,
  },
  productPriceRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  productPrice: {
    ...TypoSkin.heading7,
    color: '#FF3B30',
    marginRight: 8,
  },
  originalPrice: {
    ...TypoSkin.body3,
    color: '#757575',
    textDecorationLine: 'line-through',
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  totalLabel: {
    ...TypoSkin.body2,
    color: '#212121',
  },
  totalPrice: {
    ...TypoSkin.heading6,
    color: '#FF3B30',
  },
  actionButtonsContainer: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  cancelButton: {
    backgroundColor: '#FF3B30',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginBottom: 16,
  },
  cancelButtonText: {
    ...TypoSkin.heading7,
    color: '#FFFFFF',
  },
  actionButtonsGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  chatButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingVertical: 12,
    marginRight: 8,
  },
  chatButtonText: {
    ...TypoSkin.body2,
    color: '#212121',
    marginLeft: 8,
  },
  callButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingVertical: 12,
    marginLeft: 8,
  },
  callButtonText: {
    ...TypoSkin.body2,
    color: '#212121',
    marginLeft: 8,
  },
  bottomSpacer: {
    height: 100,
  },
});

export default OrderDetailPage;
