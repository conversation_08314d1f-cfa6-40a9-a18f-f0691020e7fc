/* eslint-disable react-native/no-inline-styles */
import {useNavigation} from '@react-navigation/native';
import React, {useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  Animated,
  Dimensions,
  Linking,
  RefreshControl,
  StyleSheet,
  TouchableOpacity,
  View,
  Image,
  Text,
} from 'react-native';
import {
  AppButton,
  FBottomSheet,
  showBottomSheet,
  Winicon,
} from 'wini-mobile-components';
import {NumberStatusIcon} from '../../Config/Contanst';
import {shopData} from '../../mock/shopData';
import {TypoSkin} from '../../assets/skin/typography';
import {ColorThemes} from '../../assets/skin/colors';

interface NavigateShopProps {
  title: string;
  status?: number;
}

const NavigateShop = (props: NavigateShopProps) => {
  let {title, status} = props;
  const navigation = useNavigation<any>();
  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <View style={styles.navigator}>
      <TouchableOpacity style={styles.back} onPress={() => handleBack()}>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <Winicon
            src="outline/arrows/left-arrow"
            size={14}
            color={'black'}
            style={styles.navigatorIcon}
          />
        </View>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <Image
            source={require('../../assets/images/logo.png')}
            style={styles.navigatorImage}
          />
        </View>
      </TouchableOpacity>
      <View style={{flex: 1, alignItems: 'center'}}>
        <Text style={styles.titleNavigator}>{title}</Text>
      </View>
      {status && status == NumberStatusIcon.One ? (
        <View style={styles.noti}>
          <View style={styles.navigatorImage}></View>
          <View style={styles.navigatorImage}></View>
        </View>
      ) : (
        <View style={styles.noti}>
          <TouchableOpacity>
            <View
              style={{
                width: 32,
                height: 32,
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '50%',
                backgroundColor:
                  ColorThemes.light.neutral_main_background_color,
              }}>
              <Winicon
                src="fill/shopping/barcode-qr"
                size={20}
                color={ColorThemes.light.secondary1_sub_color}
              />
            </View>
          </TouchableOpacity>
          <TouchableOpacity>
            <View
              style={{
                width: 32,
                height: 32,
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '50%',
                backgroundColor:
                  ColorThemes.light.neutral_main_background_color,
              }}>
              <Winicon
                src="outline/user interface/bell"
                size={20}
                color={ColorThemes.light.secondary1_sub_color}
              />
            </View>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  navigator: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomColor: '#00FFFF',
    paddingBottom: 18,
    borderBottomWidth: 0.5,
    width: '100%',
  },
  navigatorImage: {
    width: 32,
    height: 32,
    borderRadius: 50,
  },
  navigatorIcon: {
    marginTop: 4,
  },
  titleNavigator: {
    ...TypoSkin.title3,
    marginLeft: 20,
  },
  back: {
    display: 'flex',
    flexDirection: 'row',
    marginLeft: 15,
  },
  noti: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 15,
    gap: 2,
  },
});

export default NavigateShop;
