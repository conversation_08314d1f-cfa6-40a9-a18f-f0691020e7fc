import {useNavigation} from '@react-navigation/native';
import {
  differenceInDays,
  differenceInHours,
  differenceInMinutes,
} from 'date-fns';
import {useState, useEffect} from 'react';
import {TouchableOpacity, Image, View, Text} from 'react-native';
import {useDispatch} from 'react-redux';
import {
  NotificationActions,
  NotificationItem,
} from '../../../../redux/reducers/notificationReducer';
import {decrementBadgeCount} from '../../../../features/notifications/fcm/fcm_helper';
import {Ultis} from '../../../../utils/Utils';
import {ColorThemes} from '../../../../assets/skin/colors';
import {
  AppButton,
  ComponentStatus,
  showSnackbar,
  Winicon,
} from 'wini-mobile-components';
import {TypoSkin} from '../../../../assets/skin/typography';
import {RootScreen} from '../../../../router/router';
import {DataController} from '../../../../base/baseController';

interface Props {
  item: NotificationItem | any;
  tab: number;
}

export default function NotificationCard(props: Props) {
  const {tab} = props;
  const navigation = useNavigation<any>();
  const dispatch = useDispatch<any>();
  const [item, setItem] = useState<NotificationItem>(props.item);

  const _onPress = async () => {
    NotificationActions.edit(dispatch, [{...item, Status: 1}]).then(res => {
      if (res.code === 200) {
        setItem({...item, Status: 1});
        decrementBadgeCount();
      }
    });

    switch (item?.LinkApp) {
      case RootScreen.PostDetail:
        const ptid = item.LinkWeb?.split('/post?id=')[1];
        const postController = new DataController('Posts');
        if (!ptid) return;
        const post = await postController.getById(ptid);
        if (post?.data) {
          navigation.navigate(RootScreen.navigateCommunityParent);
          navigation.push(RootScreen.PostDetail, {item: post});
        } else {
          showSnackbar({
            message: 'Bài viết không tồn tại',
            status: ComponentStatus.WARNING,
          });
        }
        break;
      case RootScreen.GroupIndex:
        const grId = item.LinkWeb?.split('/groups/d?id=')[1];
        navigation.navigate(RootScreen.navigateCommunityParent);
        navigation.push(RootScreen.GroupIndex, {Id: grId});
        break;
      case RootScreen.ProfileCommunity:
        const cusId = item.LinkWeb?.split('/profile?id=')[1];
        navigation.navigate(RootScreen.navigateCommunityParent);
        navigation.push(RootScreen.ProfileCommunity, {
          Id: cusId,
        });
        break;
      default:
        break;
    }
  };

  const getDiffrentTime = () => {
    var time = '';
    const min = differenceInMinutes(new Date(), new Date(item.DateCreated));
    if (min >= 60) {
      const hours = differenceInHours(new Date(), new Date(item.DateCreated));
      if (hours >= 24) {
        const day = differenceInDays(new Date(), new Date(item.DateCreated));
        if (day >= 10)
          var time = Ultis.datetoString(new Date(item.DateCreated), 'dd/mm');
        else time = `${day} ngày trước`;
      } else time = `${hours} giờ trước`;
    } else time = `${min ? min : 1} phút trước`;
    return time;
  };

  return (
    <TouchableOpacity
      activeOpacity={0.5}
      onPress={_onPress}
      style={{
        flexDirection: 'row',
        paddingVertical: 16,
        paddingHorizontal: 16,
        backgroundColor:
          item?.Status == 1
            ? ColorThemes.light.neutral_absolute_background_color
            : ColorThemes.light.primary_background,
        borderRadius: 8,
        gap: 12,
        alignItems: 'center',
      }}>
      <Winicon
        src="outline/business/briefcase-26"
        size={24}
        color={ColorThemes.light.neutral_text_subtitle_color}
      />
      <View style={{flex: 1}}>
        <Text
          numberOfLines={2}
          style={[TypoSkin.semibold2, {paddingBottom: 4}]}>
          {item?.Content ?? '-'}
        </Text>
        <Text
          style={[
            TypoSkin.regular1,
            {color: ColorThemes.light.neutral_text_subtitle_color},
          ]}>
          {getDiffrentTime()}
        </Text>
        {/* actions */}
        {/* <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              gap: 8,
              paddingTop: 12,
              alignItems: 'center',
            }}>
            <AppButton
              title={'Từ chối'}
              backgroundColor={
                item?.Status != 1
                  ? ColorThemes.light.neutral_absolute_background_color
                  : ColorThemes.light.neutral_main_background_color
              }
              borderColor="transparent"
              containerStyle={{
                height: 35,
                flex: 1,
                borderRadius: 8,
                paddingHorizontal: 12,
                paddingVertical: 5,
              }}
              onPress={async () => {
                NotificationActions.edit(dispatch, [{...item, Status: 1}]);
              }}
              textColor={ColorThemes.light.neutral_text_subtitle_color}
            />
            <AppButton
              title={'Chấp nhận'}
              backgroundColor={ColorThemes.light.primary_main_color}
              borderColor="transparent"
              containerStyle={{
                height: 35,
                flex: 1,
                borderRadius: 8,
                paddingHorizontal: 12,
                paddingVertical: 5,
              }}
              onPress={async () => {
                NotificationActions.edit(dispatch, [{...item, Status: 1}]);
              }}
              textColor={ColorThemes.light.neutral_absolute_background_color}
            />
          </View> */}
      </View>
    </TouchableOpacity>
  );
}
