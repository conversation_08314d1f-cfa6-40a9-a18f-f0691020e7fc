import React from 'react';
import { View, TextInput, StyleSheet } from 'react-native';
import { Winicon } from 'wini-mobile-components';

const SearchBar = ({ setDataSearch }: { setDataSearch: (data: string) => void }) => {
    return (
        <View style={styles.container}>
            <TextInput
                style={styles.input}
                placeholder="Bạn muốn tìm gì?"
                placeholderTextColor="#999"
                onChange={e => setDataSearch(e.nativeEvent.text)}
            />
            <Winicon src="fill/text/microphone" size={24} color="#999" />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#fff',
        borderRadius: 25, // Bo góc đểව: '2px solid #ddd',
        paddingHorizontal: 15,
        paddingVertical: 10,
        borderWidth: 0.5,
        borderColor: "#999",
        margin: 10
    },
    input: {
        flex: 1,
        fontSize: 16,
        color: '#333',
        paddingVertical: 0,
    },
    icon: {
        marginRight: 15,
    },
});

export default SearchBar;