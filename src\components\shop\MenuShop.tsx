/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  Animated,
  Dimensions,
  Linking,
  RefreshControl,
  StyleSheet,
  TouchableOpacity,
  View,
  Image,
  Text,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../router/router';
import LinearGradient from 'react-native-linear-gradient';
import {Winicon} from 'wini-mobile-components';
import {TypeMenuShop} from '../../Config/Contanst';
import {MenuShopProps} from '../dto/dto';
import {TypoSkin} from '../../assets/skin/typography';
import {ColorThemes} from '../../assets/skin/colors';

const MenuShop = (props: MenuShopProps) => {
  const {select, setSelect} = props;
  const navigation = useNavigation<any>();
  return (
    <View style={styles.tabs}>
      {select == TypeMenuShop.User ? (
        <LinearGradient
          start={{x: 0, y: 0}}
          end={{x: 1, y: 0}}
          colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
          style={{
            height: 40,
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: '#90C8Fb',
            borderRadius: 10,
            marginLeft: 16,
            marginRight: 8,
          }}>
          <View
            style={{
              alignItems: 'center',
              flexDirection: 'row',
              marginLeft: 6,
              marginRight: 4,
              marginTop: 4,
              marginBottom: 4,
            }}>
            <Winicon
              src="fill/location/explore-user"
              size={22}
              color={ColorThemes.light.primary_main_color}
            />
            <Text
              style={{
                ...TypoSkin.title3,
                marginTop: 6,
                marginBottom: 6,
                marginLeft: 4,
                height: 22,
                color: ColorThemes.light.primary_main_color,
              }}>
              Cá nhân
            </Text>
          </View>
        </LinearGradient>
      ) : (
        <TouchableOpacity
          style={styles.tab}
          onPress={() => setSelect(TypeMenuShop.User)}>
          <View
            style={{
              width: 107.5,
              height: 40,
              flexDirection: 'row',
              alignItems: 'center',
              borderRadius: 10,
              marginLeft: 16,
              marginRight: 8,
            }}>
            <View
              style={{
                alignItems: 'center',
                flexDirection: 'row',
                marginLeft: 6,
                marginRight: 4,
                marginTop: 4,
                marginBottom: 4,
              }}>
              <Winicon
                src="fill/location/explore-user"
                size={20}
                color={ColorThemes.light.neutral_text_subtitle_color}
              />
              <Text
                style={{
                  ...TypoSkin.title3,
                  marginRight: 6,
                  marginTop: 6,
                  marginBottom: 6,
                  marginLeft: 4,
                  height: 22,
                  color: ColorThemes.light.neutral_text_subtitle_color,
                }}>
                Cá nhân
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      )}
      {select == TypeMenuShop.Afiliate ? (
        <LinearGradient
          start={{x: 0, y: 0}}
          end={{x: 1, y: 0}}
          colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
          style={{
            width: 90,
            height: 40,
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: '#90C8Fb',
            borderRadius: 10,
            marginRight: 8,
            marginLeft: 8,
          }}>
          <View
            style={{
              alignItems: 'center',
              flexDirection: 'row',
              marginLeft: 6,
              marginRight: 1,
              marginTop: 8,
              marginBottom: 8,
            }}>
            <Winicon
              src="color/business/currency-dollar"
              size={20}
              color={ColorThemes.light.primary_main_color}
            />
            <Text
              style={{
                ...TypoSkin.title3,
                marginRight: 6,
                marginTop: 6,
                marginBottom: 6,
                marginLeft: 4,
                height: 22,
                color: ColorThemes.light.primary_main_color,
              }}>
              Affiliate
            </Text>
          </View>
        </LinearGradient>
      ) : (
        <TouchableOpacity
          style={{
            width: 90,
            height: 40,
            flexDirection: 'row',
            alignItems: 'center',
            borderRadius: 10,
            marginRight: 8,
            marginLeft: 8,
          }}
          onPress={() => setSelect(TypeMenuShop.Afiliate)}>
          <View
            style={{
              alignItems: 'center',
              flexDirection: 'row',
              marginLeft: 6,
              marginRight: 1,
              marginTop: 8,
              marginBottom: 8,
            }}>
            <Winicon
              src="color/business/currency-dollar"
              size={20}
              color={ColorThemes.light.neutral_text_subtitle_color}
            />
            <Text
              style={{
                ...TypoSkin.title3,
                marginRight: 6,
                marginTop: 6,
                marginBottom: 6,
                marginLeft: 4,
                height: 22,
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}>
              Affiliate
            </Text>
          </View>
        </TouchableOpacity>
      )}

      {select == TypeMenuShop.Shop ? (
        <View>
          <LinearGradient
            start={{x: 0, y: 0}}
            end={{x: 1, y: 0}}
            colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
            style={{
              width: 77,
              height: 40,
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: '#90C8Fb',
              borderRadius: 10,
              marginLeft: 8,
              marginRight: 8,
            }}>
            <View
              style={{
                alignItems: 'center',
                flexDirection: 'row',
                marginLeft: 6,
                marginRight: 1,
                marginTop: 8,
                marginBottom: 8,
              }}>
              <Winicon
                src="fill/shopping/store"
                size={21}
                color={ColorThemes.light.primary_main_color}
              />
              <Text
                style={{
                  ...TypoSkin.title3,
                  marginRight: 6,
                  marginTop: 6,
                  marginBottom: 6,
                  marginLeft: 4,
                  height: 22,
                  color: ColorThemes.light.primary_main_color,
                }}>
                Shop
              </Text>
            </View>
          </LinearGradient>
        </View>
      ) : (
        <TouchableOpacity onPress={() => setSelect(TypeMenuShop.Shop)}>
          <View
            style={{
              width: 77,
              height: 40,
              flexDirection: 'row',
              alignItems: 'center',
              borderRadius: 10,
              marginLeft: 8,
              marginRight: 8,
            }}>
            <View
              style={{
                alignItems: 'center',
                flexDirection: 'row',
                marginLeft: 6,
                marginRight: 1,
                marginTop: 8,
                marginBottom: 8,
              }}>
              <Winicon
                src="fill/shopping/store"
                size={21}
                color={ColorThemes.light.neutral_text_subtitle_color}
              />
              <Text
                style={{
                  ...TypoSkin.title3,
                  marginRight: 6,
                  marginTop: 6,
                  marginBottom: 6,
                  marginLeft: 4,
                  height: 22,
                  color: ColorThemes.light.neutral_text_subtitle_color,
                }}>
                Shop
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      )}
      {select == TypeMenuShop.Wallet ? (
        <View>
          <LinearGradient
            start={{x: 0, y: 0}}
            end={{x: 1, y: 0}}
            colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
            style={{
              width: 51,
              height: 40,
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: '#90C8Fb',
              borderRadius: 10,
              marginLeft: 8,
              marginRight: 52.5,
            }}>
            <View
              style={{
                alignItems: 'center',
                flexDirection: 'row',
                marginLeft: 6,
                marginRight: 1,
                marginTop: 8,
                marginBottom: 8,
              }}>
              <Winicon
                src="fill/shopping/wallet-44"
                size={21}
                color={ColorThemes.light.primary_main_color}
              />
              <Text
                style={{
                  ...TypoSkin.title3,
                  marginRight: 6,
                  marginTop: 6,
                  marginBottom: 6,
                  marginLeft: 4,
                  height: 22,
                  color: ColorThemes.light.primary_main_color,
                }}>
                Ví
              </Text>
            </View>
          </LinearGradient>
        </View>
      ) : (
        <TouchableOpacity
          style={styles.tab}
          onPress={() => navigation.push(RootScreen.MyWalletProfile)}>
          <View
            style={{
              width: 51,
              height: 40,
              flexDirection: 'row',
              alignItems: 'center',
              borderRadius: 10,
              marginLeft: 8,
              marginRight: 52.5,
            }}>
            <View
              style={{
                alignItems: 'center',
                flexDirection: 'row',
                marginLeft: 6,
                marginRight: 1,
                marginTop: 8,
                marginBottom: 8,
              }}>
              <Winicon
                src="fill/shopping/wallet-44"
                size={21}
                color="#262626"
              />
              <Text
                style={{
                  ...TypoSkin.title3,
                  marginRight: 6,
                  marginTop: 6,
                  marginBottom: 6,
                  marginLeft: 4,
                  height: 22,
                }}>
                Ví
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  tabs: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    marginTop: 22,
    maxHeight: 40,
    maxWidth: 440,
  },
  tab: {
    width: 107,
    flexDirection: 'row',
    alignItems: 'center',
  },
  tabWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 16,
    marginRight: 16,
    width: 107,
  },
  linearGradient: {
    width: 107.5,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#90C8Fb',
    borderRadius: 10,
    marginLeft: 16,
  },
  tabText: {
    fontSize: 16,
    color: '#262626',
    marginLeft: 5,
  },
  tabTextAction: {
    ...TypoSkin.title3,
    color: '#1C33FF',
    marginLeft: 5,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#2196F3',
  },
  activeTabText: {
    color: '#2196F3',
    fontWeight: 'bold',
  },
  ImageIcon: {
    width: 22,
    height: 22,
  },
});

export default MenuShop;
