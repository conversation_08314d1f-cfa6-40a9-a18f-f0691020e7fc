import React, { useState } from 'react';
import {
    ScrollView,
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    FlatList,
    Dimensions
} from "react-native";
import HeaderShop from "../../components/shop/HeaderShop";
import NavigateShop from "../../components/shop/NavigateShop";
import { ColorThemes } from "../../assets/skin/colors";
import { TypoSkin } from "../../assets/skin/typography";
import { Winicon } from 'wini-mobile-components';
import { useNavigation } from '@react-navigation/native';
import { DataController } from '../../base/baseController';
import { useSelectorCustomerState } from '../../redux/hook/customerHook';

const { width } = Dimensions.get('window');

const TransactionHistory = () => {
    const navigation = useNavigation();
    const [selectedFilter, setSelectedFilter] = useState('all');
    const [loading, setLoading] = useState(false);
    const [transactionHistory, settransactionHistory] = useState<any[]>([]);
    const customer = useSelectorCustomerState().data;
    const handleBack = ()  => {
        navigation.goBack();
    };
    const fetchData = async () => {
            setLoading(true);
            // Gọi API để lấy dữ liệu ví
            const controller = new DataController('HistoryReward');
            const res = await controller.getListSimple({
                page: 1,
                size: 5,
                query: `@CustomerId: {${customer.Id}}`,
                returns: ['Id', 'Name', 'ParentId', 'ListParent', 'AvatarUrl'],
                sortby: { BY: 'DateCreated', DIRECTION: 'ASC' },
            });
            if (res.code === 200) {
                const data = res.data.map((item: any) => {
                    return {
                        id: item.Id,
                        type: item.Value > 0 ? 'income' : 'expense',
                        amount: item.Value,
                        description: item.Description,
                        time: item.DateCreated,
                    };
                });
                settransactionHistory(data);
            }
            setLoading(false);
        };
    // Mock data - thay thế bằng data thực từ API
    const allTransactions = [
        {
            id: 1,
            type: 'income',
            amount: 20000000,
            description: 'Hoa hồng đơn hàng #1234545 từ "Nguyễn Thanh Tùng"',
            time: '17:40 - 06 Dec 2024',
            icon: 'fill/user interface/plus'
        },
        {
            id: 2,
            type: 'expense',
            amount: -20000000,
            description: 'Hoa hồng đơn hàng #1234545 từ "Nguyễn Thanh Tùng"',
            time: '17:40 - 06 Dec 2024',
            icon: 'fill/user interface/minus'
        },
        {
            id: 3,
            type: 'expense',
            amount: -20000000,
            description: 'Hoa hồng đơn hàng #1234545 từ "Nguyễn Thanh Tùng"',
            time: '17:40 - 06 Dec 2024',
            icon: 'fill/user interface/minus'
        },
        {
            id: 4,
            type: 'expense',
            amount: -20000000,
            description: 'Hoa hồng đơn hàng #1234545 từ "Nguyễn Thanh Tùng"',
            time: '17:40 - 06 Dec 2024',
            icon: 'fill/user interface/minus'
        },
        {
            id: 5,
            type: 'income',
            amount: 20000000,
            description: 'Hoa hồng đơn hàng #1234545 từ "Nguyễn Thanh Tùng"',
            time: '17:40 - 06 Dec 2024',
            icon: 'fill/user interface/plus'
        }
    ];

    const filterOptions = [
        { id: 'all', label: 'Tất cả', icon: 'fill/user interface/settings' },
        { id: 'income', label: 'Vào', icon: 'fill/user interface/plus' },
        { id: 'expense', label: 'Ra', icon: 'fill/user interface/minus' }
    ];

    const getFilteredTransactions = () => {
        if (selectedFilter === 'all') return allTransactions;
        return allTransactions.filter(transaction => transaction.type === selectedFilter);
    };

    const formatMoney = (amount: number) => {
        return Math.abs(amount).toLocaleString('vi-VN');
    };

    const renderFilterButton = (filter: any) => (
        <TouchableOpacity
            key={filter.id}
            style={[
                styles.filterButton,
                selectedFilter === filter.id && styles.filterButtonActive
            ]}
            onPress={() => setSelectedFilter(filter.id)}
        >
            <Winicon
                src={filter.icon}
                size={16}
                color={selectedFilter === filter.id ? 'white' : ColorThemes.light.neutral_text_title_color}
            />
            <Text style={[
                styles.filterButtonText,
                selectedFilter === filter.id && styles.filterButtonTextActive
            ]}>
                {filter.label}
            </Text>
        </TouchableOpacity>
    );

    const renderTransactionItem = ({ item }: any) => (
        <View style={styles.transactionItem}>
            <View style={[
                styles.transactionIcon,
                { backgroundColor: item.type === 'income' ? ColorThemes.light.secondary2_background : ColorThemes.light.error_background }
            ]}>
                <Winicon
                    src={item.icon}
                    size={16}
                    color={item.type === 'income' ? ColorThemes.light.secondary2_main_color : ColorThemes.light.error_main_color}
                />
            </View>
            <View style={styles.transactionContent}>
                <View style={styles.transactionHeader}>
                    <Text style={[
                        styles.transactionAmount,
                        { color: item.type === 'income' ? ColorThemes.light.secondary2_main_color : ColorThemes.light.error_main_color }
                    ]}>
                        {item.type === 'income' ? '+' : '-'} {formatMoney(item.amount)} GCT
                    </Text>
                </View>
                <Text style={styles.transactionDescription} numberOfLines={2}>
                    {item.description}
                </Text>
                <Text style={styles.transactionTime}>
                    {item.time}
                </Text>
            </View>
        </View>
    );

    const filteredTransactions = getFilteredTransactions();

    return (
        <View style={styles.container}>
            <HeaderShop />
            <View style={styles.customHeader}>
                <TouchableOpacity style={styles.backButton} onPress={handleBack}>
                    <Winicon
                        src="outline/arrows/left-arrow"
                        size={14}
                        color={'black'}
                    />
                    <View style={styles.logoContainer}>
                        <View style={styles.logo} />
                    </View>
                </TouchableOpacity>
                <Text style={styles.headerTitle}>Lịch sử giao dịch</Text>
                <TouchableOpacity style={styles.searchButton}>
                    <Winicon
                        src="outline/user interface/search"
                        size={20}
                        color={ColorThemes.light.neutral_text_title_color}
                    />
                </TouchableOpacity>
            </View>
            
            {/* Filter Section */}
            <View style={styles.filterSection}>
                <View style={styles.filterContainer}>
                    {filterOptions.map(renderFilterButton)}
                </View>
            </View>

            {/* Transaction Count */}
            <View style={styles.countSection}>
                <Text style={styles.countText}>
                    Tổng {filteredTransactions.length} giao dịch
                </Text>
            </View>

            {/* Transaction List */}
            <FlatList
                data={filteredTransactions}
                renderItem={renderTransactionItem}
                keyExtractor={(item) => item.id.toString()}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.listContainer}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f8f9fa',
    },
    customHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 15,
        paddingBottom: 18,
        borderBottomWidth: 0.5,
        borderBottomColor: '#00FFFF',
        backgroundColor: 'white',
    },
    backButton: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    logoContainer: {
        marginLeft: 8,
    },
    logo: {
        width: 32,
        height: 32,
        borderRadius: 16,
        backgroundColor: ColorThemes.light.primary_main_color,
    },
    headerTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: ColorThemes.light.neutral_text_title_color,
        flex: 1,
        textAlign: 'center',
        marginLeft: -40, // Để căn giữa title
    },
    searchButton: {
        padding: 8,
        borderRadius: 20,
        backgroundColor: 'transparent',
    },
    filterSection: {
        backgroundColor: 'white',
        paddingHorizontal: 20,
        paddingVertical: 16,
        marginBottom: 10,
    },
    filterContainer: {
        flexDirection: 'row',
        gap: 12,
    },
    filterButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        backgroundColor: ColorThemes.light.neutral_background_color,
        gap: 6,
    },
    filterButtonActive: {
        backgroundColor: ColorThemes.light.neutral_text_title_color,
    },
    filterButtonText: {
        fontSize: 14,
        color: ColorThemes.light.neutral_text_title_color,
        fontWeight: '500',
    },
    filterButtonTextActive: {
        color: 'white',
    },
    countSection: {
        backgroundColor: 'white',
        paddingHorizontal: 20,
        paddingVertical: 12,
        marginBottom: 10,
    },
    countText: {
        fontSize: 14,
        color: ColorThemes.light.neutral_text_title_color,
        fontWeight: '500',
    },
    listContainer: {
        backgroundColor: 'white',
        paddingHorizontal: 20,
        paddingTop: 10,
    },
    transactionItem: {
        flexDirection: 'row',
        paddingVertical: 16,
        borderBottomWidth: 1,
        borderBottomColor: ColorThemes.light.neutral_border_color,
    },
    transactionIcon: {
        width: 40,
        height: 40,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
    },
    transactionContent: {
        flex: 1,
    },
    transactionHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 4,
    },
    transactionAmount: {
        fontSize: 16,
        fontWeight: '600',
    },
    transactionDescription: {
        fontSize: 14,
        color: ColorThemes.light.neutral_text_title_color,
        marginBottom: 4,
        lineHeight: 20,
    },
    transactionTime: {
        fontSize: 12,
        color: ColorThemes.light.neutral_text_subtitle_color,
    },
});

export default TransactionHistory;
