import React, {use, useEffect, useState} from 'react';
import {
  View,
  Image,
  Text,
  TouchableOpacity,
  StyleSheet,
  Pressable,
} from 'react-native';
import {FlatList, ScrollView} from 'react-native-gesture-handler';
import {Winicon} from 'wini-mobile-components';
import {TypeMenuReview} from '../../../Config/Contanst';
import ReviewInfo from '../ReviewInfo';

const ReviewProductIteCard = (
  {item, index}: {item: any; index: number},
  type: string,
) => {
  return (
    <Pressable style={styles.wrapper} key={`key ${index}`}>
      <View style={styles.review}>
        <ReviewInfo item={item} index={index} />
        <View style={styles.reviewContent}>
          <Text style={styles.description}>{item.description}</Text>
          <ScrollView style={styles.imagesProduct} horizontal={true}>
            {item?.imagesProduct?.map((image: string, index: number) => (
              <Image
                key={`item-${index}`}
                source={{uri: image}}
                style={styles.avatarProduct}
              />
            ))}
          </ScrollView>
          {type == TypeMenuReview.Product ? (
            <View style={styles.reviewDetail}>
              <View style={styles.imageDetail}>
                <Image
                  source={{
                    uri: item?.product?.productImage,
                  }}
                  style={styles.avatarDetail}
                />
              </View>
              <View style={{justifyContent: 'center'}}>
                <Text style={styles.tag}>{item?.product?.productName}</Text>
                <Text style={styles.size}>{item?.product?.property}</Text>
              </View>
            </View>
          ) : (
            <View style={styles.reviewDetail}>
              <View style={styles.orderDetail}>
                <Winicon src="fill/shopping/a-chart" size={20} color="blue" />
                <Text style={styles.orderName}>{item?.Order?.orderId}</Text>
              </View>
            </View>
          )}
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    padding: 10,
  },

  review: {
    flexDirection: 'column',
    padding: 10,
    borderBottomWidth: 0.2,
    borderBottomColor: '#00FFFF',
    marginLeft: 10,
    width: '100%',
  },

  reviewContent: {
    flex: 1,
    marginLeft: 58,
  },

  description: {
    fontSize: 14,
    marginVertical: 5,
  },
  imagesProduct: {
    flexDirection: 'row',
    marginTop: 10,
    width: '100%',
    height: 100,
  },
  images: {
    flexDirection: 'row',
    marginTop: 10,
  },
  productImage: {
    width: 100,
    height: 100,
    marginRight: 10,
    borderRadius: 5,
  },
  reviewDetail: {
    flexDirection: 'row',
    alignContent: 'center',
  },
  avatarProduct: {
    width: 80,
    height: 80,
    borderRadius: 20,
    marginRight: 10,
  },
  imageDetail: {
    borderWidth: 5,
    borderRadius: 50,
    width: 50,
    height: 50,
    marginRight: 10,
    borderColor: '#F8F8FF',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  avatarDetail: {
    width: 40,
    height: 40,
    padding: 20,
    borderRadius: 50,
  },
  tag: {
    fontSize: 15,
    color: '#555',
  },
  size: {
    fontSize: 12,
    color: '#888',
  },
  orderDetail: {
    flexDirection: 'row',
    margin: 10,
  },
  orderName: {
    fontSize: 20,
    color: 'blue',
    marginLeft: 5,
  },
});

export default ReviewProductIteCard;
