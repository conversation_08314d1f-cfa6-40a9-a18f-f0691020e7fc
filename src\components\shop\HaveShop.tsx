import {useNavigation} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {TextField, Winicon} from 'wini-mobile-components';
import {RootScreen} from '../../router/router';
import {Title, TypeMenuShop} from '../../Config/Contanst';
import {ScrollView} from 'react-native-gesture-handler';
import LeaderShopInfo from '../Field/LeaderShopInfo';
import {OrderData} from '../../mock/shopData';
import {useDispatch} from 'react-redux';
import {OrderActions} from '../../redux/reducers/OrderReducer';
import {useSelectorShopState} from '../../redux/hook/shopHook ';
import {useSelectorOrderState} from '../../redux/hook/orderHook ';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';

interface HaveShopProps {
  shop: any[];
}
const HaveShop = (props: HaveShopProps) => {
  const navigation = useNavigation<any>();
  const [getNewOrder, SetGetNewOrder] = useState<number>(0);
  const [getProcessingOrder, SetGetProcessingOrder] = useState<number>(0);
  const [getCancelOrder, SetGetCancelOrder] = useState<number>(0);
  const dispatch = useDispatch<any>();
  const orderInfo = useSelectorOrderState().data;
  const shopInfo = useSelectorShopState().data;

  const handleNavigateOrders = (
    order: string,
    type: string,
    status?: number,
    numberOrder?: number,
  ) => {
    navigation.navigate(order, {
      type: type,
      status: status,
      numberOrder: numberOrder,
    });
  };
  useEffect(() => {
    dispatch(OrderActions.getInforOrder(shopInfo[0]?.Id));
  }, []);
  useEffect(() => {
    SetGetNewOrder(orderInfo?.NewOrder?.number);
    SetGetProcessingOrder(orderInfo?.ProcessOrder?.number);
    SetGetCancelOrder(orderInfo?.CancelOrder?.number);
  }, [orderInfo]);
  return (
    <View style={styles.container}>
      <View style={styles.navBar}>
        <TouchableOpacity
          style={styles.navItem}
          onPress={() =>
            handleNavigateOrders(
              RootScreen.OrderDetail,
              Title.New,
              1,
              getNewOrder,
            )
          }>
          <Winicon
            src="color/business/wallet-43"
            size={15}
            color={ColorThemes.light.primary_main_color}
          />
          <Text style={styles.navText}>Đơn hàng mới</Text>
          <Text style={styles.navBadge}>{getNewOrder}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.navItem}
          onPress={() =>
            handleNavigateOrders(
              RootScreen.OrderDetail,
              Title.Processing,
              1,
              getProcessingOrder,
            )
          }>
          <Winicon src="color/transportation/car-2" size={15} color={'green'} />
          <Text style={styles.navText}>Đang xử lý</Text>
          <Text style={styles.navBadge}>{getProcessingOrder}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.navItem}
          onPress={() =>
            handleNavigateOrders(RootScreen.OrderDetail, Title.Done, 1)
          }>
          <Winicon src="color/sport/medal" size={15} color={'#FFCC66'} />
          <Text style={styles.navText}>Hoàn thành</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.navItem}
          onPress={() =>
            handleNavigateOrders(
              RootScreen.OrderDetail,
              Title.Cancel,
              1,
              getCancelOrder,
            )
          }>
          <Winicon src="color/arrows/exit-right" size={15} color={'orange'} />
          <Text style={styles.navText}>Hủy/hoàn</Text>
          <Text style={styles.navBadge}>{getCancelOrder}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.navItem}
          onPress={() => handleNavigateOrders(RootScreen.Review, Title.Shop)}>
          <Winicon
            src="outline/user interface/star"
            size={15}
            color={'#CCCC00'}
          />
          <Text style={styles.navText}>Đánh giá</Text>
        </TouchableOpacity>
      </View>

      <View
        style={{
          height: 63,
          maxWidth: 409,
          marginTop: 23,
          marginLeft: 7,
          marginRight: 3,
          backgroundColor: ColorThemes.light.primary_background,
          borderRadius: 10,
        }}>
        <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
          <View
            style={{
              flexDirection: 'column',
              margin: 10,
            }}>
            <Text
              style={{
                fontSize: 16,
                color: ColorThemes.light.neutral_text_title_color,
                fontWeight: '700',
                marginBottom: 3,
              }}>
              Số dư bán hàng
            </Text>
            <Text
              style={{
                ...TypoSkin.title4,
                color: ColorThemes.light.neutral_text_title_color,
                marginBottom: 3,
              }}>
              500.000 VNĐ
            </Text>
          </View>
          <View>
            <TouchableOpacity
            onPress={
              ()=>{
                navigation.push(RootScreen.ConfigAffiliate)
              }
            }

              style={{
                backgroundColor: ColorThemes.light.primary_main_color,
                borderRadius: 5,
                width: 64,
                height: 24,
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: 10,
                marginRight: 10,
                marginBottom: 29,
              }}>
              <Text
                style={{
                  color: ColorThemes.light.neutral_absolute_background_color,
                }}>
                Rút tiền
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Card thông tin cửa hàng */}

      <View style={{height: '67%'}}>
        <ScrollView>
          <LeaderShopInfo shop={props.shop} />
          <TouchableOpacity
            style={styles.actionCard}
            onPress={() =>
              handleNavigateOrders(RootScreen.ManageProduct, Title.MyProduct)
            }>
            <Winicon
              src="outline/user interface/shop"
              size={24}
              color={'orange'}
            />
            <Text style={styles.actionText}>QL sản phẩm</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionCard}
            onPress={() =>
              handleNavigateOrders(RootScreen.ChartReport, Title.Report)
            }>
            <Winicon
              src="fill/user interface/note"
              size={24}
              color={'orange'}
            />
            <Text style={styles.actionText}>Báo cáo</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionCard}
          onPress={()=>{
                    navigation.navigate(RootScreen.ConfigAffiliate)                        
                }}
           >
            <Winicon
              src="fill/user interface/note"
              size={24}
              color={'orange'}
            />
            <Text style={styles.actionText}>Cấu hình affiliate</Text>
          </TouchableOpacity>
          {/* <TouchableOpacity style={styles.actionCard} >
                    <Winicon src="fill/user interface/note" size={15} color={'orange'} />
                    <Text style={styles.actionText}>Quản lý thanh toán</Text>
                </TouchableOpacity> */}
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    marginTop: 18,
    marginLeft: 20,
    marginRight: 13,
  },
  navBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: 'ColorThemes.light.neutral_absolute_background_color',
  },
  navItem: {
    alignItems: 'center',
    borderColor: '#00FFFF',
    borderWidth: 0.3,
    padding: 10,
    borderRadius: 10,
    maxWidth: 67,
    maxHeight: 68,
  },
  navText: {
    ...TypoSkin.title4,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginTop: 4,
    textAlign: 'center',
  },
  navBadge: {
    position: 'absolute',
    top: 5,
    right: 4,
    backgroundColor: '#FF0000',
    color: '#fff',
    fontSize: 10,
    paddingHorizontal: 4,
    borderRadius: 10,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
  },
  cardHeaderLeft: {
    display: 'flex',
    flexDirection: 'row',
    alignContent: 'center',
    gap: 3,
    flex: 1,
  },
  cardHeaderRight: {},

  cardContent: {
    marginTop: 10,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  infoValue: {
    fontSize: 14,
    color: '#000',
    fontWeight: '500',
  },
  infoValueEdit: {
    lineHeight: 14,
    display: 'flex',
    alignItems: 'center',
    color: '#000',
    fontWeight: '500',
  },
  actionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    height: 70,
  },
  actionText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_title_color,
    marginLeft: 10,
  },

  input: {
    flex: 1,
    fontSize: 16,
    color: 'black',
    borderWidth: 0,
    backgroundColor: 'white',
    lineHeight: 14,
  },
});

export default HaveShop;
