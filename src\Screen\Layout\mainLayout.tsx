import {StyleSheet, View} from 'react-native';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {ComponentStatus, FDialog, showDialog} from 'wini-mobile-components';
import Home from '../Page/Home';
import {ColorThemes} from '../../assets/skin/colors';
import {useRoute} from '@react-navigation/native';
import {useEffect, useRef} from 'react';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import CustomTabBar from '../../components/CustomTabBar';
import ProductCarouselDemo from '../Page/ProductCarouselDemo';
import {navigateReset, RootScreen} from '../../router/router';
import Shop from '../../modules/shop/ManageShop';
import Profile from '../../modules/customer/profile';
import {useSelectorShopState} from '../../redux/hook/shopHook ';
import {useDispatch} from 'react-redux';
import {ProductActions} from '../../redux/reducers/ShoptReducer';
import ProductListByCategory from '../../modules/Product/list/ProductListByCategory';
import MyWallet from '../Page/myWallet';
const Tab = createBottomTabNavigator();

const bottomNavigateData = [
  {
    id: 0,
    name: 'Trang chủ',
    component: Home,
  },
  {
    id: 1,
    name: 'Danh mục',
    component: ProductListByCategory,
  },
  {
    id: 2,
    name: 'QR Code',
    component: MyWallet,
  },
  {
    id: 3,
    name: 'Kết nối',
    component: Home,
  },
  {
    id: 4,
    name: 'Cá nhân',
    component: Profile,
  },
  {
    id: 5,
    name: 'Thông tin shop',
    component: Shop,
  },
];

export const dialogCheckAcc = (ref: any) => {
  showDialog({
    ref: ref,
    status: ComponentStatus.WARNING,
    title: 'Vui lòng đăng nhập để sử dụng!',
    onSubmit: async () => {
      navigateReset(RootScreen.login);
    },
  });
};

export default function EComLayout() {
  const route = useRoute<any>();
  const dialogCheckAccRef = useRef<any>(null);
  const user = useSelectorCustomerState().data;
  const shop = useSelectorShopState().data;
  const dispatch = useDispatch<any>();
  return (
    <View style={styles.container}>
      <FDialog ref={dialogCheckAccRef} />
      <Tab.Navigator
        initialRouteName={route.params?.rootName ?? undefined}
        screenOptions={{
          headerShown: false,
        }}
        tabBar={props => <CustomTabBar {...props} />}>
        {bottomNavigateData.map((item, index) => {
          return (
            <Tab.Screen
              listeners={{
                tabPress: (e: any) => {
                  if (
                    !user &&
                    item.id !== 0 &&
                    item.id !== 1 &&
                    item.id !== 4
                  ) {
                    dialogCheckAcc(dialogCheckAccRef);
                    // Prevent default action
                    e.preventDefault();
                    return;
                  }
                  //Any custom code here
                },
              }}
              key={`${index}`}
              name={item.name}
              options={{
                headerShown: false,
              }}
              component={item.component}
            />
          );
        })}
      </Tab.Navigator>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    paddingBottom: 16,
  },
});
