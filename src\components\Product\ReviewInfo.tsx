import React, {use, useEffect, useState} from 'react';
import {
  View,
  Image,
  Text,
  TouchableOpacity,
  StyleSheet,
  Pressable,
} from 'react-native';
import {FlatList, ScrollView} from 'react-native-gesture-handler';
import {Rating, Winicon} from 'wini-mobile-components';
import {TypeMenuReview} from '../../Config/Contanst';

const ReviewInfo = ({item, index}: {item: any; index: number}) => {
  return (
    <Pressable style={styles.wrapper} key={`key ${index}`}>
      <View style={styles.review}>
        <Image
          source={{
            uri: item.avatar,
          }}
          style={styles.avatar}
        />
        <View style={styles.reviewContent}>
          <Text style={styles.name}>{item.UserName}</Text>
          <Text style={styles.rating}>
            <Rating value={item.rating} size={20} />
          </Text>
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  wrapper: {},

  review: {
    flexDirection: 'row',
    padding: 10,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
  },
  reviewContent: {
    flex: 1,
  },
  name: {
    fontWeight: 'bold',
    fontSize: 20,
  },
  rating: {
    color: '#FFD700', // Màu vàng cho sao
  },
  description: {
    fontSize: 14,
    marginVertical: 5,
  },
  imagesProduct: {
    flexDirection: 'row',
    marginTop: 10,
    width: '100%',
    height: 100,
  },
  images: {
    flexDirection: 'row',
    marginTop: 10,
  },
  productImage: {
    width: 100,
    height: 100,
    marginRight: 10,
    borderRadius: 5,
  },
  reviewDetail: {
    flexDirection: 'row',
    alignContent: 'center',
  },
  avatarProduct: {
    width: 80,
    height: 80,
    borderRadius: 20,
    marginRight: 10,
  },
});

export default ReviewInfo;
