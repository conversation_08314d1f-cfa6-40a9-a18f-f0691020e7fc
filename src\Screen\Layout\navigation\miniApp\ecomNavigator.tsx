import {createNativeStackNavigator} from '@react-navigation/native-stack';

import Instructors from '../../../../modules/customer/listview/instructors';
import SettingProfile from '../../../../modules/customer/setting/setting';
import {navigateReset, RootScreen} from '../../../../router/router';
import {useDispatch} from 'react-redux';
import {useEffect} from 'react';
import LoginScreen from '../../../../modules/customer/login';
import {CustomerActions} from '../../../../redux/reducers/CustomerReducer';
import {
  getDataToAsyncStorage,
  saveDataToAsyncStorage,
} from '../../../../utils/AsyncStorage';
import {SplashScreen} from '../../Splash';
import BiometricSetting from '../../../../modules/customer/setting/biometricSetting';
import NotificationIndex from '../../../../modules/notification/view';
import VnpayPaymentScreen from '../../../../utils/vnpayWebview';
import ForgotPass from '../../../../modules/customer/form/forgot-pass';
import FAQView from '../../../../modules/customer/listview/FAQView';
import IntroPage from '../../../Page/Intro';
import EComLayout from '../../mainLayout';
import ProductDetail from '../../../../modules/Product/productDetail';
import RegisterShop from '../../../../modules/shop/RegisterShop';
import OrderDetail from '../../../../modules/order/OrderDetail';
import Shop from '../../../../modules/shop/ManageShop';
import Review from '../../../../modules/shop/Review';
import LableProduct from '../../../../components/Product/LabelProduct';
import OriginProduct from '../../../../components/Product/OriginProduct';
import DetailPost from '../../../../modules/news/detail';
import HotProductsDemo from '../../../Page/HotProductsDemo';
import ProductListByCategory from '../../../../modules/Product/list/ProductListByCategory';
import {SearchIndex} from '../../../../modules/Product/list/searchIndex';
import ManageProduct from '../../../../modules/Product/ManageProduct';
import CreateNewProduct from '../../../../modules/Product/CreateNewProduct';
import CartPage from '../../../Page/CartPage';
import ChartReport from '../../../../modules/shop/ShopReport';
import CreateReviewOrderDetail from '../../../../components/Order/CreateReviewOrder';
import CreateReviewOrder from '../../../../modules/shop/CreateReviewOrder';
import MyWallet from '../../../Page/myWallet';
import ConfigAffiliate from '../../../../modules/shop/configAffiliate';
import TreeAffiliateDetail from '../../../../modules/shop/treeAffiliateDetail';
import PolicyView from '../../../../modules/customer/setting/policy';
import MyWalletProfile from '../../../Page/myWalletProfile';
import TransactionHistory from '../../../Page/TransactionHistory';

const Ecom = createNativeStackNavigator();

export function EComStackNavigator() {
  return (
    <Ecom.Navigator
      screenOptions={{headerShown: false, orientation: 'portrait'}}>
      <Ecom.Screen
        name={RootScreen.splashView}
        component={SplashScreenWithAuthCheck}
      />
      <Ecom.Screen
        name={RootScreen.login}
        component={LoginScreen}
        options={{animation: 'fade'}}
      />
      <Ecom.Screen name={RootScreen.navigateEComView} component={EComLayout} />
      <Ecom.Screen name={RootScreen.Instructors} component={Instructors} />
      <Ecom.Screen
        name={RootScreen.Notification}
        component={NotificationIndex}
      />
      <Ecom.Screen
        name={RootScreen.SettingProfile}
        component={SettingProfile}
      />
      <Ecom.Screen name={RootScreen.ProductDetail} component={ProductDetail} />
      <Ecom.Screen name={RootScreen.DetailPost} component={DetailPost} />
      <Ecom.Screen name={RootScreen.CartPage} component={CartPage} />
      <Ecom.Screen
        name={RootScreen.CheckoutPage}
        component={require('../../../Page/CheckoutPage').default}
      />
      <Ecom.Screen
        name={RootScreen.BiometricSetting}
        component={BiometricSetting}
      />
      <Ecom.Screen name={RootScreen.ForgotPass} component={ForgotPass} />
      <Ecom.Screen
        name={RootScreen.VnpayPaymentScreen}
        component={VnpayPaymentScreen}
      />
      <Ecom.Screen name={RootScreen.Intro} component={IntroPage} />
      <Ecom.Screen
        name={RootScreen.OrderDetailPage}
        component={require('../../../Page/OrderDetailPage').default}
      />
      <Ecom.Screen
        name={RootScreen.HotProductsDemo}
        component={HotProductsDemo}
      />
      <Ecom.Screen
        name={RootScreen.AllHotProductsPage}
        component={require('../../../Page/AllHotProductsPage').default}
      />
      <Ecom.Screen name={RootScreen.OrderDetail} component={OrderDetail} />
      <Ecom.Screen name={RootScreen.RegisterShop} component={RegisterShop} />
      <Ecom.Screen name={RootScreen.Shop} component={Shop} />
      <Ecom.Screen name={RootScreen.ManageProduct} component={ManageProduct} />
      <Ecom.Screen
        name={RootScreen.CreateNewProduct}
        component={CreateNewProduct}
      />
      <Ecom.Screen name={RootScreen.SearchIndex} component={SearchIndex} />
      <Ecom.Screen
        name={RootScreen.ProductListByCategory}
        component={ProductListByCategory}
      />
      <Ecom.Screen
        name={RootScreen.MyWallet}
        component={MyWallet}
      />
      <Ecom.Screen
        name={RootScreen.Review}
        component={Review}
      />
      <Ecom.Screen
        name={RootScreen.ChartReport}
        component={ChartReport}
      />
      <Ecom.Screen
        name={RootScreen.ConfigAffiliate}
        component={ConfigAffiliate}
      />
      <Ecom.Screen
        name={RootScreen.TreeAffiliateDetail}
        component={TreeAffiliateDetail}
      />
      <Ecom.Screen
        name={RootScreen.CreateReviewOrderDetail}
        component={CreateReviewOrderDetail}
      />
      <Ecom.Screen
        name={RootScreen.CreateReviewOrder}
        component={CreateReviewOrder}
      />
      <Ecom.Screen
        name={RootScreen.MyWalletProfile}
        component={MyWalletProfile}
      />
      <Ecom.Screen
        name={RootScreen.TransactionHistory}
        component={TransactionHistory}
      />
      <Ecom.Screen name={RootScreen.PolicyView} component={PolicyView} />
      <Ecom.Screen name={RootScreen.FAQView} component={FAQView} />
    </Ecom.Navigator>
  );
}

const SplashScreenWithAuthCheck = ({navigation}: any) => {
  const dispatch = useDispatch<any>();
  useEffect(() => {
    const checkAuthAndNavigate = async () => {
      try {
        // Wait for a minimum of 3 seconds for splash screen
        const splashTimer = new Promise(resolve => setTimeout(resolve, 3000));

        // Check for user token
        const tokenCheck = await getDataToAsyncStorage('accessToken');

        // Wait for both operations to complete
        const [_, accessToken] = await Promise.all([splashTimer, tokenCheck]);

        // check the first time open app and show IntroPage
        const isFirstTime = await getDataToAsyncStorage('isFirstTime');
        if (!isFirstTime) {
          await saveDataToAsyncStorage('isFirstTime', 'true');
          navigateReset(RootScreen.Intro);
          return;
        }

        // get info
        if (accessToken) {
         await dispatch(CustomerActions.getInfor());
        }

        const nextRoute =
          // accessToken
          //   ? RootScreen.navigateEComView
          //   : RootScreen.login;
          RootScreen.navigateEComView;

        navigateReset(nextRoute);
      } catch (error) {
        console.error('Authentication check error:', error);
        // Default to Auth flow if error occurs
        navigateReset(RootScreen.login);
      }
    };

    checkAuthAndNavigate();
  }, [navigation, dispatch]);

  return <SplashScreen />;
};
